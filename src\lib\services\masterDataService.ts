import { api } from '@/lib/utils/apiClient'
import { API_CONFIG } from '@/lib/config/api'
import { withCache, CACHE_KEYS } from '@/lib/utils/apiCache'

// Interfaces for Master Data
export interface Department {
  id?: number
  maKhoa: string
  tenKhoa: string
  moTa?: string
  email?: string
  soDienThoai?: string
  trangThai: boolean
  createdAt?: string
  updatedAt?: string
}

export interface Subject {
  id?: number
  maMonHoc: string
  tenMonHoc: string
  soTinChi: number
  soTietLyThuyet: number
  soTietThucHanh: number
  moTa?: string
  idKhoa: number
  tenKhoa?: string
  trangThai: boolean
  createdAt?: string
  updatedAt?: string
}

export interface ClassInfo {
  id?: number
  maLop: string
  tenLop: string
  siSo: number
  khoaHoc: string
  idKhoa: number
  tenKhoa?: string
  trangThai: boolean
  createdAt?: string
  updatedAt?: string
}

export interface Room {
  id?: number
  maPhong: string
  tenPhong: string
  sucChua: number
  loaiPhong: string
  trangThietBi?: string
  idCoSo: number
  tenCoSo?: string
  trangThai: boolean
  createdAt?: string
  updatedAt?: string
}

export interface Teacher {
  id?: number
  maCanBo: string
  tenCanBo: string
  email: string
  soDienThoai?: string
  chuyenMon?: string
  hocVi?: string
  idKhoa: number
  tenKhoa?: string
  trangThai: boolean
  createdAt?: string
  updatedAt?: string
}

export interface Campus {
  id?: number
  maCoSo: string
  tenCoSo: string
  diaChi: string
  soDienThoai?: string
  email?: string
  trangThai: boolean
  createdAt?: string
  updatedAt?: string
}

export interface PaginatedResponse<T> {
  content: T[]
  totalElements: number
  totalPages: number
  size: number
  number: number
  first: boolean
  last: boolean
}

class MasterDataService {
  // Department Management
  async getDepartments(page = 0, size = 10, search = ''): Promise<PaginatedResponse<Department>> {
    const params: Record<string, any> = { page, size }
    if (search) params.search = search

    const response = await api.get(API_CONFIG.ENDPOINTS.MASTER_DATA.DEPARTMENTS, params)
    return response.data
  }

  async getDepartmentById(id: number): Promise<Department> {
    const response = await api.get(`${API_CONFIG.ENDPOINTS.MASTER_DATA.DEPARTMENTS}/${id}`)
    return response.data
  }

  async createDepartment(department: Omit<Department, 'id'>): Promise<Department> {
    const response = await api.post(API_CONFIG.ENDPOINTS.MASTER_DATA.DEPARTMENTS, department)
    return response.data
  }

  async updateDepartment(id: number, department: Partial<Department>): Promise<Department> {
    const response = await api.put(`${API_CONFIG.ENDPOINTS.MASTER_DATA.DEPARTMENTS}/${id}`, department)
    return response.data
  }

  async deleteDepartment(id: number): Promise<void> {
    await api.delete(`${API_CONFIG.ENDPOINTS.MASTER_DATA.DEPARTMENTS}/${id}`)
  }

  // Subject Management
  async getSubjects(page = 0, size = 10, search = ''): Promise<PaginatedResponse<Subject>> {
    const params: Record<string, any> = { page, size }
    if (search) params.search = search

    const response = await api.get(API_CONFIG.ENDPOINTS.MASTER_DATA.SUBJECTS, params)
    return response.data
  }

  async createSubject(subject: Omit<Subject, 'id'>): Promise<Subject> {
    const response = await api.post(API_CONFIG.ENDPOINTS.MASTER_DATA.SUBJECTS, subject)
    return response.data
  }

  async updateSubject(id: number, subject: Partial<Subject>): Promise<Subject> {
    const response = await api.put(`${API_CONFIG.ENDPOINTS.MASTER_DATA.SUBJECTS}/${id}`, subject)
    return response.data
  }

  async deleteSubject(id: number): Promise<void> {
    await api.delete(`${API_CONFIG.ENDPOINTS.MASTER_DATA.SUBJECTS}/${id}`)
  }

  // Class Management
  async getClasses(page = 0, size = 10, search = ''): Promise<PaginatedResponse<ClassInfo>> {
    const params: Record<string, any> = { page, size }
    if (search) params.search = search

    const response = await api.get(API_CONFIG.ENDPOINTS.MASTER_DATA.CLASSES, params)
    return response.data
  }

  async createClass(classInfo: Omit<ClassInfo, 'id'>): Promise<ClassInfo> {
    const response = await api.post(API_CONFIG.ENDPOINTS.MASTER_DATA.CLASSES, classInfo)
    return response.data
  }

  async updateClass(id: number, classInfo: Partial<ClassInfo>): Promise<ClassInfo> {
    const response = await api.put(`${API_CONFIG.ENDPOINTS.MASTER_DATA.CLASSES}/${id}`, classInfo)
    return response.data
  }

  async deleteClass(id: number): Promise<void> {
    await api.delete(`${API_CONFIG.ENDPOINTS.MASTER_DATA.CLASSES}/${id}`)
  }

  // Room Management
  async getRooms(page = 0, size = 10, search = ''): Promise<PaginatedResponse<Room>> {
    const params: Record<string, any> = { page, size }
    if (search) params.search = search

    const response = await api.get(API_CONFIG.ENDPOINTS.MASTER_DATA.ROOMS, params)
    return response.data
  }

  async createRoom(room: Omit<Room, 'id'>): Promise<Room> {
    const response = await api.post(API_CONFIG.ENDPOINTS.MASTER_DATA.ROOMS, room)
    return response.data
  }

  async updateRoom(id: number, room: Partial<Room>): Promise<Room> {
    const response = await api.put(`${API_CONFIG.ENDPOINTS.MASTER_DATA.ROOMS}/${id}`, room)
    return response.data
  }

  async deleteRoom(id: number): Promise<void> {
    await api.delete(`${API_CONFIG.ENDPOINTS.MASTER_DATA.ROOMS}/${id}`)
  }

  // Teacher Management
  async getTeachers(page = 0, size = 10, search = ''): Promise<PaginatedResponse<Teacher>> {
    const params: Record<string, any> = { page, size }
    if (search) params.search = search

    const response = await api.get(API_CONFIG.ENDPOINTS.MASTER_DATA.TEACHERS, params)
    return response.data
  }

  async createTeacher(teacher: Omit<Teacher, 'id'>): Promise<Teacher> {
    const response = await api.post(API_CONFIG.ENDPOINTS.MASTER_DATA.TEACHERS, teacher)
    return response.data
  }

  async updateTeacher(id: number, teacher: Partial<Teacher>): Promise<Teacher> {
    const response = await api.put(`${API_CONFIG.ENDPOINTS.MASTER_DATA.TEACHERS}/${id}`, teacher)
    return response.data
  }

  async deleteTeacher(id: number): Promise<void> {
    await api.delete(`${API_CONFIG.ENDPOINTS.MASTER_DATA.TEACHERS}/${id}`)
  }

  // Campus Management
  async getCampuses(page = 0, size = 10, search = ''): Promise<PaginatedResponse<Campus>> {
    const params: Record<string, any> = { page, size }
    if (search) params.search = search

    const response = await api.get(API_CONFIG.ENDPOINTS.MASTER_DATA.CAMPUSES, params)
    return response.data
  }

  async createCampus(campus: Omit<Campus, 'id'>): Promise<Campus> {
    const response = await api.post(API_CONFIG.ENDPOINTS.MASTER_DATA.CAMPUSES, campus)
    return response.data
  }

  async updateCampus(id: number, campus: Partial<Campus>): Promise<Campus> {
    const response = await api.put(`${API_CONFIG.ENDPOINTS.MASTER_DATA.CAMPUSES}/${id}`, campus)
    return response.data
  }

  async deleteCampus(id: number): Promise<void> {
    await api.delete(`${API_CONFIG.ENDPOINTS.MASTER_DATA.CAMPUSES}/${id}`)
  }

  // Utility methods
  async getAllDepartments(): Promise<Department[]> {
    const response = await api.get(`${API_CONFIG.ENDPOINTS.MASTER_DATA.DEPARTMENTS}/all`)
    return response.data
  }

  async getAllCampuses(): Promise<Campus[]> {
    const response = await api.get(`${API_CONFIG.ENDPOINTS.MASTER_DATA.CAMPUSES}/all`)
    return response.data
  }

  // Get all subjects (for schedule form)
  async getAllSubjects(): Promise<Subject[]> {
    const response = await api.get(`${API_CONFIG.ENDPOINTS.MASTER_DATA.SUBJECTS}/all`)
    return response.data
  }

  // Get subjects by department
  async getSubjectsByDepartment(departmentId: number): Promise<Subject[]> {
    const response = await api.get(`${API_CONFIG.ENDPOINTS.MASTER_DATA.SUBJECTS}`, {
      idKhoa: departmentId,
      size: 1000 // Get all subjects for the department
    })
    return response.data.content || response.data
  }

  // Get all classes (for schedule form)
  async getAllClasses(): Promise<ClassInfo[]> {
    const response = await api.get(`${API_CONFIG.ENDPOINTS.MASTER_DATA.CLASSES}/all`)
    return response.data
  }

  // Get classes by department
  async getClassesByDepartment(departmentId: number): Promise<ClassInfo[]> {
    const response = await api.get(`${API_CONFIG.ENDPOINTS.MASTER_DATA.CLASSES}`, {
      idKhoa: departmentId,
      size: 1000 // Get all classes for the department
    })
    return response.data.content || response.data
  }

  // Get all teachers (for schedule form)
  async getAllTeachers(): Promise<Teacher[]> {
    const response = await api.get(`${API_CONFIG.ENDPOINTS.MASTER_DATA.TEACHERS}/all`)
    return response.data
  }

  // Get teachers by department
  async getTeachersByDepartment(departmentId: number): Promise<Teacher[]> {
    const response = await api.get(`${API_CONFIG.ENDPOINTS.MASTER_DATA.TEACHERS}`, {
      idKhoa: departmentId,
      size: 1000 // Get all teachers for the department
    })
    return response.data.content || response.data
  }

  // Get all rooms (for schedule form)
  async getAllRooms(): Promise<Room[]> {
    const response = await api.get(`${API_CONFIG.ENDPOINTS.MASTER_DATA.ROOMS}/all`)
    return response.data
  }

  // Get rooms by campus
  async getRoomsByCampus(campusId: number): Promise<Room[]> {
    const response = await api.get(`${API_CONFIG.ENDPOINTS.MASTER_DATA.ROOMS}`, {
      idCoSo: campusId,
      size: 1000 // Get all rooms for the campus
    })
    return response.data.content || response.data
  }

  // Get rooms by campus and type
  async getRoomsByCampusAndType(campusId: number, roomType: string): Promise<Room[]> {
    const response = await api.get(`${API_CONFIG.ENDPOINTS.MASTER_DATA.ROOMS}`, {
      idCoSo: campusId,
      loaiPhong: roomType,
      size: 1000 // Get all rooms for the campus and type
    })
    return response.data.content || response.data
  }

  // Get statistics
  async getMasterDataStats(): Promise<{
    departments: number
    subjects: number
    classes: number
    rooms: number
    teachers: number
    campuses: number
  }> {
    const fallbackData = {
      departments: 12,
      subjects: 245,
      classes: 89,
      rooms: 67,
      teachers: 156,
      campuses: 3
    }

    return withCache(
      CACHE_KEYS.MASTER_DATA_STATS,
      async () => {
        try {
          const response = await api.get('/api/master-data/stats')
          return response.data
        } catch (error) {
          console.info('📊 Using mock data for Master data stats (API not available)')
          return fallbackData
        }
      },
      3 * 60 * 1000 // Cache for 3 minutes
    )
  }
}

export const masterDataService = new MasterDataService()
export default masterDataService
