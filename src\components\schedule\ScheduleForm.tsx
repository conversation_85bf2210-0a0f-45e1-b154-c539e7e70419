'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Save, X, BookOpen, Users, Clock, MapPin, User, Calendar } from 'lucide-react'
import { masterDataService, Department, Subject, ClassInfo, Room, Teacher, Campus } from '@/lib/services/masterDataService'

interface ScheduleFormData {
  // Khoa và lớp
  department: string
  class: string
  studyType: 'LT' | 'TH' | '' // Lý thuyết hoặc Thực hành
  group: string // Chỉ hiện khi chọn TH

  // <PERSON>ôn học và bài học
  subject: string
  lesson: string
  lessonType: 'LT' | 'TH' | ''
  periods: number // Số tiết
  coefficient: number // Hệ số

  // Gi<PERSON><PERSON> viên
  teacher: string

  // Thời gian
  dayOfWeek: string
  session: 'morning' | 'afternoon' | 'evening' | ''

  // Địa điểm
  campus: string
  areaType: 'LT' | 'TH' | '' // Khu LT hoặc TH
  room: string
}

interface ScheduleFormProps {
  initialData?: Partial<ScheduleFormData>
  selectedDate?: string
  timeInfo?: { session?: string; timeSlot?: string }
  onSave: (data: ScheduleFormData) => void
  onCancel: () => void
  isEdit?: boolean
}

export default function ScheduleForm({
  initialData,
  selectedDate,
  timeInfo,
  onSave,
  onCancel,
  isEdit = false
}: ScheduleFormProps) {
  const [formData, setFormData] = useState<ScheduleFormData>({
    department: '',
    class: '',
    studyType: '',
    group: '',
    subject: '',
    lesson: '',
    lessonType: '',
    periods: 1,
    coefficient: 1,
    teacher: '',
    dayOfWeek: '',
    session: '',
    campus: '',
    areaType: '',
    room: '',
    ...initialData
  })

  // Auto-set from calendar selection
  useEffect(() => {
    if (!isEdit && selectedDate && timeInfo) {
      const correctDayOfWeek = getDayOfWeekFromDate(selectedDate)
      setFormData(prev => ({
        ...prev,
        dayOfWeek: correctDayOfWeek,
        session: timeInfo.session || 'morning'
      }))
    }
  }, [selectedDate, timeInfo, isEdit])

  const [loading, setLoading] = useState(false)

  // API data state
  const [departments, setDepartments] = useState<Department[]>([])
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [classes, setClasses] = useState<ClassInfo[]>([])
  const [teachers, setTeachers] = useState<Teacher[]>([])
  const [rooms, setRooms] = useState<Room[]>([])
  const [campuses, setCampuses] = useState<Campus[]>([])
  const [apiLoading, setApiLoading] = useState(true)

  // Load initial data from APIs
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        setApiLoading(true)

        // Load all master data in parallel
        const [
          departmentsData,
          campusesData
        ] = await Promise.all([
          masterDataService.getAllDepartments(),
          masterDataService.getAllCampuses()
        ])

        setDepartments(departmentsData)
        setCampuses(campusesData)
      } catch (error) {
        console.error('Error loading initial data:', error)
        // Set fallback data if API fails
        setDepartments([
          { id: 1, maKhoa: 'CNTT', tenKhoa: 'Khoa Công nghệ thông tin', trangThai: true },
          { id: 2, maKhoa: 'DTVT', tenKhoa: 'Khoa Điện tử viễn thông', trangThai: true },
          { id: 3, maKhoa: 'CK', tenKhoa: 'Khoa Cơ khí', trangThai: true }
        ])
        setCampuses([
          { id: 1, maCoSo: 'CS1', tenCoSo: 'Cơ sở 1', diaChi: 'Địa chỉ CS1', trangThai: true },
          { id: 2, maCoSo: 'CS2', tenCoSo: 'Cơ sở 2', diaChi: 'Địa chỉ CS2', trangThai: true }
        ])
      } finally {
        setApiLoading(false)
      }
    }

    loadInitialData()
  }, [])

  // Auto-set time info from calendar click
  useEffect(() => {
    if (!isEdit) {
      setFormData(prev => {
        const updates: Partial<ScheduleFormData> = {}

        // Set day of week from selected date
        if (selectedDate) {
          const dayOfWeek = getDayOfWeekFromDate(selectedDate)
          updates.dayOfWeek = dayOfWeek
        }

        // Set session if provided
        if (timeInfo?.session) {
          updates.session = timeInfo.session as 'morning' | 'afternoon' | 'evening'
        }

        // Set time slot if provided
        if (timeInfo?.timeSlot) {
          const endTime = getEndTimeFromStart(timeInfo.timeSlot)
          updates.periods = calculatePeriods(timeInfo.timeSlot, endTime)
        }

        return { ...prev, ...updates }
      })
    }
  }, [selectedDate, timeInfo, isEdit])

  // Load classes and subjects when department changes
  useEffect(() => {
    const loadDepartmentData = async () => {
      if (formData.department) {
        try {
          const departmentId = parseInt(formData.department)
          const [classesData, subjectsData] = await Promise.all([
            masterDataService.getClassesByDepartment(departmentId),
            masterDataService.getSubjectsByDepartment(departmentId)
          ])
          setClasses(classesData)
          setSubjects(subjectsData)
        } catch (error) {
          console.error('Error loading department data:', error)
        }
      } else {
        setClasses([])
        setSubjects([])
      }
    }

    loadDepartmentData()
  }, [formData.department])

  // Load teachers when department changes
  useEffect(() => {
    const loadTeachers = async () => {
      if (formData.department) {
        try {
          const departmentId = parseInt(formData.department)
          const teachersData = await masterDataService.getTeachersByDepartment(departmentId)
          setTeachers(teachersData)
        } catch (error) {
          console.error('Error loading teachers:', error)
        }
      } else {
        setTeachers([])
      }
    }

    loadTeachers()
  }, [formData.department])

  // Load rooms when campus or area type changes
  useEffect(() => {
    const loadRooms = async () => {
      if (formData.campus) {
        try {
          const campusId = parseInt(formData.campus)
          let roomsData: Room[]

          if (formData.areaType) {
            roomsData = await masterDataService.getRoomsByCampusAndType(campusId, formData.areaType)
          } else {
            roomsData = await masterDataService.getRoomsByCampus(campusId)
          }

          setRooms(roomsData)
        } catch (error) {
          console.error('Error loading rooms:', error)
        }
      } else {
        setRooms([])
      }
    }

    loadRooms()
  }, [formData.campus, formData.areaType])

  const getDayOfWeekFromDate = (dateString: string) => {
    const date = new Date(dateString + 'T00:00:00') // Add time to avoid timezone issues
    const dayIndex = date.getDay() // 0 = Sunday, 1 = Monday, etc.

    const dayMapping = {
      0: 'sunday',    // Chủ nhật
      1: 'monday',    // Thứ 2
      2: 'tuesday',   // Thứ 3
      3: 'wednesday', // Thứ 4
      4: 'thursday',  // Thứ 5
      5: 'friday',    // Thứ 6
      6: 'saturday'   // Thứ 7
    }

    return dayMapping[dayIndex as keyof typeof dayMapping] || 'monday'
  }

  const getEndTimeFromStart = (startTime: string) => {
    const [hours, minutes] = startTime.split(':').map(Number)
    const endHour = hours + 2 // Default 2-hour duration
    return `${endHour.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
  }

  const calculatePeriods = (startTime: string, endTime: string) => {
    const [startHour] = startTime.split(':').map(Number)
    const [endHour] = endTime.split(':').map(Number)
    return Math.max(1, endHour - startHour) // At least 1 period
  }

  // Helper function to get lessons by subject (mock data for now)
  const getLessonsBySubject = (subjectId: string) => {
    // This would typically come from the backend based on the subject
    // For now, using mock data structure
    const lessonMap: { [key: string]: Array<{id: string, name: string, types: ('LT'|'TH')[], defaultType: 'LT'|'TH'}> } = {
      '1': [
        { id: 'lesson-1', name: 'Bài 1: Giới thiệu môn học', types: ['LT'], defaultType: 'LT' },
        { id: 'lesson-2', name: 'Bài 2: Lý thuyết cơ bản', types: ['LT', 'TH'], defaultType: 'LT' },
        { id: 'lesson-3', name: 'Bài 3: Thực hành', types: ['LT', 'TH'], defaultType: 'TH' },
        { id: 'lesson-4', name: 'Bài 4: Ôn tập', types: ['LT'], defaultType: 'LT' }
      ]
    }
    return lessonMap[subjectId] || []
  }

  const daysOfWeek = [
    { id: 'monday', name: 'Thứ 2' },
    { id: 'tuesday', name: 'Thứ 3' },
    { id: 'wednesday', name: 'Thứ 4' },
    { id: 'thursday', name: 'Thứ 5' },
    { id: 'friday', name: 'Thứ 6' },
    { id: 'saturday', name: 'Thứ 7' },
    { id: 'sunday', name: 'Chủ nhật' }
  ]

  const sessions = [
    { id: 'morning', name: 'Buổi sáng', time: '06:00 - 12:00' },
    { id: 'afternoon', name: 'Buổi chiều', time: '12:00 - 18:00' },
    { id: 'evening', name: 'Buổi tối', time: '18:00 - 23:00' }
  ]

  const areaTypes = [
    { id: 'LT', name: 'Khu Lý thuyết' },
    { id: 'TH', name: 'Khu Thực hành' }
  ]

  const groups = ['Nhóm 1', 'Nhóm 2', 'Nhóm 3', 'Nhóm 4', 'Nhóm 5']

  const handleInputChange = (field: keyof ScheduleFormData, value: any) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value }

      // Reset dependent fields
      if (field === 'department') {
        newData.class = ''
        newData.subject = ''
        newData.lesson = ''
        newData.lessonType = ''
        newData.teacher = ''
      }

      if (field === 'studyType' && value !== 'TH') {
        newData.group = ''
      }

      if (field === 'subject') {
        newData.lesson = ''
        newData.lessonType = ''
      }

      if (field === 'lesson') {
        const lessons = getLessonsBySubject(prev.subject)
        const selectedLesson = lessons.find(l => l.id === value)
        if (selectedLesson) {
          newData.lessonType = selectedLesson.defaultType
        }
      }

      if (field === 'dayOfWeek') {
        newData.session = ''
      }

      if (field === 'campus') {
        newData.areaType = ''
        newData.room = ''
      }

      if (field === 'areaType') {
        newData.room = ''
      }

      return newData
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Debug logging
    console.log('Form submitted with data:', formData)
    console.log('Form is valid:', isFormValid())

    if (!isFormValid()) {
      console.log('Form validation failed. Missing fields:')
      const requiredFields = [
        { name: 'department', value: formData.department },
        { name: 'class', value: formData.class },
        { name: 'studyType', value: formData.studyType },
        { name: 'subject', value: formData.subject },
        { name: 'lesson', value: formData.lesson },
        { name: 'lessonType', value: formData.lessonType },
        { name: 'teacher', value: formData.teacher },
        { name: 'dayOfWeek', value: formData.dayOfWeek },
        { name: 'session', value: formData.session },
        { name: 'campus', value: formData.campus },
        { name: 'areaType', value: formData.areaType },
        { name: 'room', value: formData.room }
      ]

      requiredFields.forEach(field => {
        if (!field.value || field.value.trim() === '') {
          console.log(`- Missing: ${field.name}`)
        }
      })

      if (formData.studyType === 'TH' && (!formData.group || formData.group.trim() === '')) {
        console.log('- Missing: group (required for TH)')
      }

      return
    }

    setLoading(true)

    try {
      await onSave(formData)
    } catch (error) {
      console.error('Error saving schedule:', error)
    } finally {
      setLoading(false)
    }
  }

  const isFormValid = () => {
    const requiredFields = [
      formData.department,
      formData.class,
      formData.studyType,
      formData.subject,
      formData.lesson,
      formData.lessonType,
      formData.teacher,
      formData.dayOfWeek,
      formData.session,
      formData.campus,
      formData.areaType,
      formData.room
    ]

    // Check if all required fields are filled
    const allFieldsFilled = requiredFields.every(field => field && field.trim() !== '')

    // Check periods is valid
    const validPeriods = formData.periods > 0

    // Check group is filled if studyType is TH
    const validGroup = formData.studyType !== 'TH' || (formData.group && formData.group.trim() !== '')

    return allFieldsFilled && validPeriods && validGroup
  }

  // Helper function to check if a field is invalid
  const isFieldInvalid = (fieldValue: string) => {
    return !fieldValue || fieldValue.trim() === ''
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Thông tin lớp học */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Thông tin lớp học
            </CardTitle>
            <CardDescription>
              Chọn khoa và lớp học
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="department" className={isFieldInvalid(formData.department) ? 'text-red-600' : ''}>
                Khoa *
              </Label>
              <Select
                value={formData.department}
                onValueChange={(value) => handleInputChange('department', value)}
                disabled={apiLoading}
              >
                <SelectTrigger className={isFieldInvalid(formData.department) ? 'border-red-500' : ''}>
                  <SelectValue placeholder={apiLoading ? "Đang tải..." : "Chọn khoa"} />
                </SelectTrigger>
                <SelectContent>
                  {departments.map(dept => (
                    <SelectItem key={dept.id} value={dept.id?.toString() || ''}>
                      {dept.tenKhoa}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="class">Lớp học *</Label>
              <Select
                value={formData.class}
                onValueChange={(value) => handleInputChange('class', value)}
                disabled={!formData.department || classes.length === 0}
              >
                <SelectTrigger>
                  <SelectValue placeholder={!formData.department ? "Chọn khoa trước" : classes.length === 0 ? "Không có lớp học" : "Chọn lớp học"} />
                </SelectTrigger>
                <SelectContent>
                  {classes.map(classInfo => (
                    <SelectItem key={classInfo.id} value={classInfo.id?.toString() || ''}>
                      {classInfo.tenLop}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="studyType">Hình thức học *</Label>
              <Select value={formData.studyType} onValueChange={(value) => handleInputChange('studyType', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Chọn hình thức" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="LT">Lý thuyết (LT)</SelectItem>
                  <SelectItem value="TH">Thực hành (TH)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {formData.studyType === 'TH' && (
              <div>
                <Label htmlFor="group">Nhóm học *</Label>
                <Select value={formData.group} onValueChange={(value) => handleInputChange('group', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn nhóm" />
                  </SelectTrigger>
                  <SelectContent>
                    {groups.map(group => (
                      <SelectItem key={group} value={group}>
                        {group}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Thông tin môn học */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BookOpen className="h-5 w-5 mr-2" />
              Thông tin môn học
            </CardTitle>
            <CardDescription>
              Chọn môn học và bài học
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="subject">Môn học/Mô đun *</Label>
              <Select
                value={formData.subject}
                onValueChange={(value) => handleInputChange('subject', value)}
                disabled={!formData.department || subjects.length === 0}
              >
                <SelectTrigger>
                  <SelectValue placeholder={!formData.department ? "Chọn khoa trước" : subjects.length === 0 ? "Không có môn học" : "Chọn môn học"} />
                </SelectTrigger>
                <SelectContent>
                  {subjects.map(subject => (
                    <SelectItem key={subject.id} value={subject.id?.toString() || ''}>
                      {subject.tenMonHoc}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="lesson">Bài học *</Label>
              <Select
                value={formData.lesson}
                onValueChange={(value) => handleInputChange('lesson', value)}
                disabled={!formData.subject}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn bài học" />
                </SelectTrigger>
                <SelectContent>
                  {getLessonsBySubject(formData.subject).map(lesson => (
                    <SelectItem key={lesson.id} value={lesson.id}>
                      <div className="flex items-center justify-between w-full">
                        <span>{lesson.name}</span>
                        <div className="flex gap-1 ml-2">
                          {lesson.types.map(type => (
                            <Badge key={type} variant="outline" className="text-xs">
                              {type}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="lessonType">Hình thức *</Label>
                <Select value={formData.lessonType} onValueChange={(value) => handleInputChange('lessonType', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="LT/TH" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="LT">LT</SelectItem>
                    <SelectItem value="TH">TH</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="periods">Số tiết *</Label>
                <Input
                  type="number"
                  min="1"
                  max="10"
                  value={formData.periods}
                  onChange={(e) => handleInputChange('periods', parseInt(e.target.value) || 1)}
                />
              </div>

              <div>
                <Label htmlFor="coefficient">Hệ số</Label>
                <Input
                  type="number"
                  step="0.1"
                  min="0.1"
                  max="3"
                  value={formData.coefficient}
                  onChange={(e) => handleInputChange('coefficient', parseFloat(e.target.value) || 1)}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* Thông tin giảng viên */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2" />
              Thông tin giảng viên
            </CardTitle>
            <CardDescription>
              Chọn giảng viên
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="teacher">Giảng viên *</Label>
              <Select
                value={formData.teacher}
                onValueChange={(value) => handleInputChange('teacher', value)}
                disabled={!formData.department || teachers.length === 0}
              >
                <SelectTrigger>
                  <SelectValue placeholder={!formData.department ? "Chọn khoa trước" : teachers.length === 0 ? "Không có giảng viên" : "Chọn giảng viên"} />
                </SelectTrigger>
                <SelectContent>
                  {teachers.map(teacher => (
                    <SelectItem key={teacher.id} value={teacher.id?.toString() || ''}>
                      {teacher.tenCanBo}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Thông tin thời gian */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Thông tin thời gian
            </CardTitle>
            <CardDescription>
              Chọn thứ và buổi học
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="dayOfWeek">Thứ giảng *</Label>
              <Select key={`dayOfWeek-${formData.dayOfWeek}`} value={formData.dayOfWeek} onValueChange={(value) => handleInputChange('dayOfWeek', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Chọn thứ" />
                </SelectTrigger>
                <SelectContent>
                  {daysOfWeek.map(day => (
                    <SelectItem key={day.id} value={day.id}>
                      {day.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="session">Buổi học *</Label>
              <Select
                key={`session-${formData.session}`}
                value={formData.session}
                onValueChange={(value) => handleInputChange('session', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn buổi" />
                </SelectTrigger>
                <SelectContent>
                  {sessions.map(session => (
                    <SelectItem key={session.id} value={session.id}>
                      <div className="flex flex-col">
                        <span>{session.name}</span>
                        <span className="text-xs text-gray-500">{session.time}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Thông tin địa điểm */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MapPin className="h-5 w-5 mr-2" />
            Thông tin địa điểm
          </CardTitle>
          <CardDescription>
            Chọn cơ sở, khu và phòng học
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div>
              <Label htmlFor="campus">Cơ sở *</Label>
              <Select
                value={formData.campus}
                onValueChange={(value) => handleInputChange('campus', value)}
                disabled={apiLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder={apiLoading ? "Đang tải..." : "Chọn cơ sở"} />
                </SelectTrigger>
                <SelectContent>
                  {campuses.map(campus => (
                    <SelectItem key={campus.id} value={campus.id?.toString() || ''}>
                      {campus.tenCoSo}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="areaType">Khu *</Label>
              <Select
                value={formData.areaType}
                onValueChange={(value) => handleInputChange('areaType', value)}
                disabled={!formData.campus}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn khu" />
                </SelectTrigger>
                <SelectContent>
                  {areaTypes.map(area => (
                    <SelectItem key={area.id} value={area.id}>
                      {area.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="room">Phòng học *</Label>
              <Select
                value={formData.room}
                onValueChange={(value) => handleInputChange('room', value)}
                disabled={!formData.campus || !formData.areaType || rooms.length === 0}
              >
                <SelectTrigger>
                  <SelectValue placeholder={
                    !formData.campus ? "Chọn cơ sở trước" :
                    !formData.areaType ? "Chọn khu trước" :
                    rooms.length === 0 ? "Không có phòng học" :
                    "Chọn phòng"
                  } />
                </SelectTrigger>
                <SelectContent>
                  {rooms.map(room => (
                    <SelectItem key={room.id} value={room.id?.toString() || ''}>
                      {room.tenPhong}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Submit buttons */}
      <div className="flex justify-end space-x-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          <X className="h-4 w-4 mr-2" />
          Hủy
        </Button>
        <Button type="submit" disabled={!isFormValid() || loading}>
          <Save className="h-4 w-4 mr-2" />
          {loading ? 'Đang lưu...' : (isEdit ? 'Cập nhật' : 'Tạo lịch')}
        </Button>
        {!isFormValid() && (
          <p className="text-sm text-red-600 mt-2">
            Vui lòng điền đầy đủ tất cả các trường bắt buộc (*)
          </p>
        )}
      </div>
    </form>
  )
}
